// Enhanced Home Page Management System with Backend Integration
class HomePageManager {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.init();
    }

    async init() {
        this.bindElements();
        this.bindEvents();
        this.initializeAnimations();

        // Load dynamic content from backend
        await this.loadBackendData();
    }

    bindElements() {
        // Hero elements
        this.heroStats = document.querySelectorAll('.hero-stats .stat-number');
        this.ctaButtons = document.querySelectorAll('.cta-button');
        this.quickLinks = document.querySelectorAll('.quick-link.enhanced');
        
        // ARIA live region
        this.liveRegion = document.getElementById('aria-live-region');
    }

    bindEvents() {
        // CTA button interactions
        this.ctaButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleCTAClick(e, button);
            });
        });

        // Quick link interactions
        this.quickLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                this.handleQuickLinkClick(e, link);
            });

            link.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleQuickLinkClick(e, link);
                }
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'h') {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
                this.announceAction('Scrolled to top');
            }
        });
    }

    initializeAnimations() {
        // Animate hero stats on page load
        this.animateHeroStats();
        
        // Add intersection observer for sections
        this.observeSections();
        
        // Add parallax effect to stars
        this.initializeParallax();
    }

    animateHeroStats() {
        this.heroStats.forEach((stat, index) => {
            const finalValue = stat.textContent;
            const numericValue = parseFloat(finalValue.replace(/[^\d.]/g, ''));
            const suffix = finalValue.replace(/[\d.]/g, '');
            
            if (!isNaN(numericValue)) {
                // Animate from 0 to final value
                let currentValue = 0;
                const increment = numericValue / 60;
                const duration = 2000; // 2 seconds
                const stepTime = duration / 60;
                
                setTimeout(() => {
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= numericValue) {
                            currentValue = numericValue;
                            clearInterval(timer);
                        }
                        
                        stat.textContent = Math.floor(currentValue) + suffix;
                    }, stepTime);
                }, index * 300); // Stagger animations
            }
        });
    }

    observeSections() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // Observe sections for animation
        const sections = document.querySelectorAll('.section, .quick-access');
        sections.forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });
    }

    initializeParallax() {
        const stars = document.querySelectorAll('.star');
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            stars.forEach((star, index) => {
                const speed = 0.1 + (index % 3) * 0.05;
                star.style.transform = `translateY(${rate * speed}px)`;
            });
        });
    }

    handleCTAClick(event, button) {
        const href = button.getAttribute('href');
        const text = button.textContent.trim();
        
        this.announceAction(`Navigating to ${text}`);
        
        // Add click animation
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
    }

    handleQuickLinkClick(event, link) {
        const href = link.getAttribute('href');
        const title = link.querySelector('.quick-link-title')?.textContent || 'Unknown';
        
        this.announceAction(`Opening ${title}`);
        
        // Add click animation
        link.style.transform = 'scale(0.98)';
        setTimeout(() => {
            link.style.transform = '';
        }, 150);
    }

    announceAction(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;

            // Clear the message after a short delay
            setTimeout(() => {
                this.liveRegion.textContent = '';
            }, 1000);
        }
    }

    // ===== BACKEND INTEGRATION METHODS =====

    async loadBackendData() {
        try {
            console.log('🎵 Loading Home Page data from backend...');

            // Load all home page data in parallel
            const [
                statsResponse,
                featuredResponse,
                trendingResponse,
                newReleasesResponse,
                recommendationsResponse,
                recentActivityResponse
            ] = await Promise.all([
                fetch(`${this.apiBase}/home/<USER>
                fetch(`${this.apiBase}/home/<USER>
                fetch(`${this.apiBase}/home/<USER>
                fetch(`${this.apiBase}/home/<USER>
                fetch(`${this.apiBase}/home/<USER>
                fetch(`${this.apiBase}/home/<USER>
            ]);

            // Process responses
            if (statsResponse.ok) {
                const stats = await statsResponse.json();
                this.updateHeroStats(stats);
            }

            if (featuredResponse.ok) {
                const featured = await featuredResponse.json();
                this.renderFeaturedContent(featured);
            }

            if (trendingResponse.ok) {
                const trending = await trendingResponse.json();
                this.renderTrendingContent(trending);
            }

            if (newReleasesResponse.ok) {
                const newReleases = await newReleasesResponse.json();
                this.renderNewReleases(newReleases);
            }

            if (recommendationsResponse.ok) {
                const recommendations = await recommendationsResponse.json();
                this.renderRecommendations(recommendations);
            }

            if (recentActivityResponse.ok) {
                const recentActivity = await recentActivityResponse.json();
                this.renderRecentActivity(recentActivity);
            }

            console.log('✅ Home Page data loaded successfully!');
            this.announceAction('Home page loaded with fresh content!');

        } catch (error) {
            console.error('❌ Error loading backend data:', error);
            this.announceAction('Using demo content - server unavailable');
        }
    }

    updateHeroStats(stats) {
        const statNumbers = document.querySelectorAll('.hero-stats .stat-number');
        if (statNumbers.length >= 3) {
            // Animate the stat numbers with real data
            this.animateStatNumber(statNumbers[0], stats.totalTracks);
            this.animateStatNumber(statNumbers[1], stats.totalArtists);
            this.animateStatNumber(statNumbers[2], stats.totalAlbums);
        }
        console.log('📊 Hero stats updated:', stats);
    }

    animateStatNumber(element, targetValue) {
        // Extract number from string like "50M+" -> 50
        const numericValue = parseInt(targetValue.toString().replace(/[^\d]/g, '')) || 0;
        const suffix = targetValue.toString().replace(/[\d]/g, '');

        const startValue = 0;
        const duration = 2000; // 2 seconds
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.round(startValue + (numericValue - startValue) * easeOutQuart);

            element.textContent = currentValue + suffix;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    renderFeaturedContent(featured) {
        // Update the featured/trending section with real data
        this.renderCarouselSection('.trending .carousel-track', featured.tracks, 'trending');
        console.log(`✅ Rendered ${featured.tracks.length} featured tracks`);
    }

    renderTrendingContent(trending) {
        // Update trending section
        this.renderCarouselSection('.trending .carousel-track', trending, 'trending');
        console.log(`✅ Rendered ${trending.length} trending tracks`);
    }

    renderNewReleases(newReleases) {
        // Update new releases section
        this.renderCarouselSection('.new-releases .carousel-track', newReleases, 'new-releases');
        console.log(`✅ Rendered ${newReleases.length} new releases`);
    }

    renderRecommendations(recommendations) {
        // Update recommended section if it exists
        const recommendedSection = document.querySelector('.recommended .carousel-track');
        if (recommendedSection && recommendations.length > 0) {
            this.renderCarouselSection('.recommended .carousel-track', recommendations, 'recommended');
            console.log(`✅ Rendered ${recommendations.length} recommendations`);
        }
    }

    renderRecentActivity(recentActivity) {
        // Could be used for a "Continue Listening" section
        if (recentActivity.tracks.length > 0) {
            console.log(`📱 Recent activity: ${recentActivity.tracks.length} tracks, ${recentActivity.playlists.length} playlists`);
        }
    }

    renderCarouselSection(selector, tracks, sectionType) {
        const container = document.querySelector(selector);
        if (!container || !tracks.length) return;

        // Clear existing content
        container.innerHTML = '';

        tracks.forEach((track, index) => {
            const carouselCard = document.createElement('div');
            carouselCard.className = 'carousel-card';
            carouselCard.innerHTML = `
                <div class="card" data-track-id="${track.id}">
                    <div class="img-container">
                        <img src="${track.cover || 'imgs/album-01.png'}" alt="${track.title}" loading="lazy" />
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play ${track.title}">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>${track.title}</h3>
                            <p>Artist: ${track.artist} • Genre: ${track.genre}</p>
                        </div>
                        <a href="#" class="button play-track-btn" data-track-id="${track.id}">Play Now</a>
                    </div>
                </div>
            `;
            container.appendChild(carouselCard);
        });

        // Add event listeners for the new cards
        this.bindCarouselCardEvents(container);
    }

    bindCarouselCardEvents(container) {
        // Play button events
        container.querySelectorAll('.play-button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const card = btn.closest('.card');
                const trackId = card.dataset.trackId;
                this.playTrack(trackId);
            });
        });

        // Play Now button events
        container.querySelectorAll('.play-track-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const trackId = btn.dataset.trackId;
                this.playTrack(trackId);
            });
        });

        // Card click events
        container.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', (e) => {
                const trackId = card.dataset.trackId;
                this.showTrackDetails(trackId);
            });
        });
    }

    playTrack(trackId) {
        this.announceAction(`Playing track ${trackId}`);
        console.log(`🎵 Playing track: ${trackId}`);
        // Here you would integrate with your music player
    }

    showTrackDetails(trackId) {
        this.announceAction(`Showing details for track ${trackId}`);
        console.log(`📋 Showing details for track: ${trackId}`);
        // Here you would show track details modal or navigate to track page
    }

    // Method to update hero statistics
    updateHeroStats(stats) {
        const elements = {
            songs: this.heroStats[0],
            artists: this.heroStats[1],
            albums: this.heroStats[2]
        };

        Object.keys(stats).forEach(key => {
            if (elements[key]) {
                elements[key].textContent = stats[key];
            }
        });
    }

    // Method to add loading shimmer effect
    addLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.add('loading-shimmer');
        });
    }

    removeLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.remove('loading-shimmer');
        });
    }

    // Method to handle responsive behavior
    handleResize() {
        // Recalculate animations on resize
        this.initializeParallax();
    }
}

// Enhanced Star Animation System
class StarAnimationManager {
    constructor() {
        this.stars = document.querySelectorAll('.star');
        this.init();
    }

    init() {
        this.enhanceStars();
        this.addInteractivity();
    }

    enhanceStars() {
        this.stars.forEach((star, index) => {
            // Add random colors
            const colors = ['var(--neon-blue)', 'var(--cosmic-pink)', 'var(--electric-violet)', 'var(--cyber-lime)'];
            const color = colors[index % colors.length];
            
            star.style.setProperty('--star-color', color);
            star.style.background = color;
            
            // Add random sizes
            const size = 1 + Math.random() * 2;
            star.style.width = `${size}px`;
            star.style.height = `${size}px`;
            
            // Add random animation delays
            const delay = Math.random() * 5;
            star.style.animationDelay = `${delay}s`;
        });
    }

    addInteractivity() {
        // Add mouse interaction
        document.addEventListener('mousemove', (e) => {
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            this.stars.forEach((star, index) => {
                const speed = 0.02 + (index % 3) * 0.01;
                const x = (mouseX - 0.5) * speed * 50;
                const y = (mouseY - 0.5) * speed * 50;
                
                star.style.transform = `translate(${x}px, ${y}px)`;
            });
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize home page manager
    window.homeManager = new HomePageManager();
    
    // Initialize star animation manager
    window.starManager = new StarAnimationManager();
    
    // Handle window resize
    window.addEventListener('resize', () => {
        window.homeManager.handleResize();
    });
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { HomePageManager, StarAnimationManager };
}
