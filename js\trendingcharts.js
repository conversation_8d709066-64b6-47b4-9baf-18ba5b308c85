// Enhanced Trending Charts Management System with Backend Integration
class TrendingChartsManager {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.currentChart = 'global';
        this.currentTimeRange = 'weekly';
        this.currentGenre = 'all';
        this.isLoading = false;
        this.currentAudio = null;
        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.loadInitialData();
    }

    bindElements() {
        // Chart navigation
        this.chartTabs = document.querySelectorAll('.chart-tab');
        this.timeRangeFilter = document.getElementById('timeRangeFilter');
        this.genreFilter = document.getElementById('genreFilter');

        // Featured hero
        this.featuredChartHero = document.getElementById('featuredChartHero');
        this.heroBgImage = document.getElementById('heroBgImage');
        this.heroTitle = document.getElementById('heroTitle');
        this.heroArtist = document.getElementById('heroArtist');
        this.heroPlays = document.getElementById('heroPlays');
        this.heroLikes = document.getElementById('heroLikes');
        this.heroShares = document.getElementById('heroShares');
        this.heroPlayBtn = document.getElementById('heroPlayBtn');
        this.heroLikeBtn = document.getElementById('heroLikeBtn');
        this.heroAddBtn = document.getElementById('heroAddBtn');

        // Chart sections
        this.songsChartList = document.getElementById('songsChartList');
        this.artistsChartGrid = document.getElementById('artistsChartGrid');
        this.albumsChartGrid = document.getElementById('albumsChartGrid');
        this.risingChartList = document.getElementById('risingChartList');

        // Statistics
        this.totalPlays = document.getElementById('totalPlays');
        this.totalTracks = document.getElementById('totalTracks');
        this.totalArtists = document.getElementById('totalArtists');
        this.totalCountries = document.getElementById('totalCountries');

        // Loading state
        this.loadingState = document.getElementById('loadingState');
        this.liveRegion = document.getElementById('aria-live-region');
    }

    bindEvents() {
        // Chart tabs
        this.chartTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const chartType = e.target.dataset.chart;
                this.setActiveChart(chartType);
            });
        });

        // Filters
        this.timeRangeFilter.addEventListener('change', (e) => {
            this.currentTimeRange = e.target.value;
            this.loadChartData();
        });

        this.genreFilter.addEventListener('change', (e) => {
            this.currentGenre = e.target.value;
            this.loadChartData();
        });

        // Hero actions
        this.heroPlayBtn.addEventListener('click', () => {
            this.playFeaturedTrack();
        });

        this.heroLikeBtn.addEventListener('click', () => {
            this.likeFeaturedTrack();
        });

        this.heroAddBtn.addEventListener('click', () => {
            this.addFeaturedToPlaylist();
        });

        // View all buttons
        document.querySelectorAll('.view-all-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const type = e.target.dataset.type;
                this.viewAllCharts(type);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.refreshCharts();
            }
        });

        // Play preview from mini player (songs, rising, featured)
        document.body.addEventListener('click', (e) => {
            // Songs/Rising: .chart-action-btn.play with data-preview
            const playBtn = e.target.closest('.chart-action-btn.play');
            if (playBtn && playBtn.dataset.preview !== undefined) {
                let title = '', artist = '', cover = '';
                // If inside a chart-item, get info from DOM
                const chartItem = playBtn.closest('.chart-item');
                if (chartItem) {
                    title = chartItem.querySelector('.chart-item-title')?.textContent || '';
                    artist = chartItem.querySelector('.chart-item-artist')?.textContent || '';
                    cover = chartItem.querySelector('img')?.src || '';
                } else if (playBtn === this.heroPlayBtn) {
                    // Featured hero play button
                    title = playBtn.getAttribute('data-title') || this.heroTitle.textContent;
                    artist = playBtn.getAttribute('data-artist') || this.heroArtist.textContent.replace('by ', '');
                    cover = playBtn.getAttribute('data-cover') || this.heroBgImage.src;
                }
                const previewUrl = playBtn.dataset.preview;
                console.log('[TrendingCharts] Play button clicked:', { previewUrl, title, artist, cover });
                // Use MiniPlayer from main.js
                if (window.miniPlayer && typeof window.miniPlayer.playTrack === 'function') {
                    window.miniPlayer.playTrack({
                        src: previewUrl,
                        title,
                        artist,
                        artwork: cover
                    });
                }
            }
        });
    }

    loadInitialData() {
        this.showLoadingState();

        // Simulate API call delay
        setTimeout(() => {
            this.loadChartData();
            this.hideLoadingState();
        }, 1500);
    }

    setActiveChart(chartType) {
        this.currentChart = chartType;

        // Update active tab
        this.chartTabs.forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-chart="${chartType}"]`).classList.add('active');

        this.loadChartData();
        this.announceChartChange(chartType);
    }

    async fetchDeezerCharts() {
        this.showLoadingState();
        try {
            // Use a CORS proxy for development
            const response = await fetch('https://corsproxy.io/?https://api.deezer.com/chart');
            const data = await response.json();
            // Map Deezer data to your UI structure
            const featured = data.tracks.data[0];
            this.renderChartData({
                featured: {
                    id: featured.id,
                    image: featured.album.cover_xl || featured.album.cover_big || featured.album.cover,
                    title: featured.title,
                    artist: featured.artist.name,
                    plays: featured.rank ? featured.rank + ' rank' : '',
                    preview: featured.preview // 30s mp3 preview
                },
                songs: data.tracks.data.map(track => ({
                    id: track.id,
                    image: track.album.cover_medium || track.album.cover,
                    title: track.title,
                    artist: track.artist.name,
                    plays: track.rank ? track.rank + ' rank' : '',
                    preview: track.preview
                })),
                artists: data.artists.data.map(artist => ({
                    id: artist.id,
                    image: artist.picture_medium || artist.picture,
                    name: artist.name,
                    genre: '', // Deezer artist object does not include genre directly
                    followers: artist.nb_fan ? artist.nb_fan.toLocaleString() : '',
                })),
                albums: data.albums.data.map(album => ({
                    id: album.id,
                    image: album.cover_medium || album.cover,
                    title: album.title,
                    artist: album.artist.name,
                    year: album.release_date ? album.release_date.split('-')[0] : '',
                    tracks: album.nb_tracks
                })),
                rising: data.tracks.data.slice(5, 10).map(track => ({
                    id: track.id,
                    image: track.album.cover_medium || track.album.cover,
                    title: track.title,
                    artist: track.artist.name,
                    plays: track.rank ? track.rank + ' rank' : '',
                    preview: track.preview
                })),
                statistics: {
                    totalPlays: '',
                    totalTracks: data.tracks.total,
                    totalArtists: data.artists.total,
                    totalCountries: 'N/A'
                }
            });
        } catch (error) {
            console.error('Failed to fetch Deezer charts:', error);
        }
        this.hideLoadingState();
    }

    loadChartData() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.fetchBackendCharts().then(() => {
            this.isLoading = false;
        });
    }

    async fetchBackendCharts() {
        this.showLoadingState();
        try {
            console.log('🎵 Loading Charts data from backend...');

            const response = await fetch(`${this.apiBase}/charts?period=${this.currentTimeRange}&region=${this.currentChart}`);

            if (response.ok) {
                const data = await response.json();
                this.renderChartData({
                    featured: data.featured ? {
                        id: data.featured.id,
                        image: data.featured.cover,
                        title: data.featured.title,
                        artist: data.featured.artist,
                        plays: data.featured.plays.toLocaleString() + ' plays',
                        likes: data.featured.likes?.toLocaleString() + ' likes',
                        preview: data.featured.preview || ''
                    } : null,
                    songs: data.songs.map(track => ({
                        id: track.id,
                        image: track.cover,
                        title: track.title,
                        artist: track.artist,
                        plays: track.plays.toLocaleString() + ' plays',
                        position: track.position,
                        previousPosition: track.previousPosition,
                        preview: track.preview || ''
                    })),
                    artists: data.artists.map(artist => ({
                        id: artist.name.replace(/\s+/g, '-').toLowerCase(),
                        image: artist.image,
                        name: artist.name,
                        genre: artist.genre,
                        followers: artist.followers,
                        position: artist.position
                    })),
                    albums: data.albums.map(album => ({
                        id: album.title.replace(/\s+/g, '-').toLowerCase(),
                        image: album.image,
                        title: album.title,
                        artist: album.artist,
                        year: album.year,
                        tracks: album.trackCount,
                        position: album.position
                    })),
                    rising: data.rising.map(track => ({
                        id: track.id,
                        image: track.cover,
                        title: track.title,
                        artist: track.artist,
                        plays: '+' + track.growthPercent + '%',
                        position: track.position,
                        preview: track.preview || ''
                    })),
                    statistics: {
                        totalPlays: '2.4B+',
                        totalTracks: data.metadata.totalTracks.toLocaleString(),
                        totalArtists: data.metadata.totalArtists.toLocaleString(),
                        totalCountries: '195'
                    }
                });

                console.log('✅ Charts data loaded successfully!');
                this.announceAction('Charts updated with latest data!');
            } else {
                throw new Error('Charts API failed');
            }
        } catch (error) {
            console.error('❌ Error loading charts data:', error);
            this.announceAction('Using demo content - server unavailable');
            // Keep existing Deezer fallback
            this.fetchDeezerCharts();
        }
        this.hideLoadingState();
    }

    async generateMockChartData() {
        // Use a set of unique, royalty-free SoundHelix demo tracks for variety
        const soundHelixTracks = [
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3',
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-4.mp3',
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-5.mp3',
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-6.mp3',
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-7.mp3',
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-8.mp3',
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-9.mp3',
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-10.mp3',
        ];

        const mockSongs = [
            { id: 1, title: 'Cosmic Dreams', artist: 'Stellar Waves', plays: '2.4M', change: 'up', changeValue: '+3', image: 'imgs/album-01.png', preview: 'https://cdns-preview-7.dzcdn.net/stream/c-7b7e7e7e7e7e7e7e7e7e7e7e7e7e7e7e-3.mp3' },
            { id: 2, title: 'Neon Nights', artist: 'Electric Pulse', plays: '2.1M', change: 'down', changeValue: '-1', image: 'imgs/album-02.png', preview: 'https://cdns-preview-8.dzcdn.net/stream/c-8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e-3.mp3' },
            { id: 3, title: 'Digital Love', artist: 'Cyber Hearts', plays: '1.9M', change: 'up', changeValue: '+2', image: 'imgs/album-03.png', preview: 'https://cdns-preview-9.dzcdn.net/stream/c-9b9e9e9e9e9e9e9e9e9e9e9e9e9e9e9e-3.mp3' },
            { id: 4, title: 'Midnight Vibes', artist: 'Luna Echo', plays: '1.7M', change: 'new', changeValue: 'NEW', image: 'imgs/album-04.png', preview: 'https://cdns-preview-1.dzcdn.net/stream/c-1b1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e-3.mp3' },
            { id: 5, title: 'Quantum Beat', artist: 'Atomic Sound', plays: '1.5M', change: 'up', changeValue: '+5', image: 'imgs/album-05.png', preview: 'https://cdns-preview-2.dzcdn.net/stream/c-2b2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e-3.mp3' },
            { id: 6, title: 'Stellar Journey', artist: 'Cosmic Drift', plays: '1.3M', change: 'down', changeValue: '-2', image: 'imgs/album-01.png', preview: 'https://cdns-preview-3.dzcdn.net/stream/c-3b3e3e3e3e3e3e3e3e3e3e3e3e3e3e3e-3.mp3' },
            { id: 7, title: 'Electric Dreams', artist: 'Neon Pulse', plays: '1.2M', change: 'up', changeValue: '+1', image: 'imgs/album-02.png', preview: 'https://cdns-preview-4.dzcdn.net/stream/c-4b4e4e4e4e4e4e4e4e4e4e4e4e4e4e4e-3.mp3' },
            { id: 8, title: 'Future Waves', artist: 'Digital Storm', plays: '1.1M', change: 'up', changeValue: '+4', image: 'imgs/album-03.png', preview: 'https://cdns-preview-5.dzcdn.net/stream/c-5b5e5e5e5e5e5e5e5e5e5e5e5e5e5e5e-3.mp3' },
            { id: 9, title: 'Cyber Nights', artist: 'Tech Beats', plays: '1.0M', change: 'down', changeValue: '-3', image: 'imgs/album-04.png', preview: 'https://cdns-preview-6.dzcdn.net/stream/c-6b6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e-3.mp3' },
            { id: 10, title: 'Galactic Sounds', artist: 'Space Echo', plays: '950K', change: 'new', changeValue: 'NEW', image: 'imgs/album-05.png', preview: 'https://cdns-preview-0.dzcdn.net/stream/c-0b0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e-3.mp3' }
        ];

        // Assign a unique SoundHelix track to each artist
        const mockArtists = [
            { id: 1, name: 'Stellar Waves', genre: 'Electronic', followers: '2.1M', image: 'imgs/artist.png.png', preview: soundHelixTracks[0] },
            { id: 2, name: 'Electric Pulse', genre: 'Synthwave', followers: '1.8M', image: 'imgs/artist.png.png', preview: soundHelixTracks[1] },
            { id: 3, name: 'Cyber Hearts', genre: 'Future Pop', followers: '1.5M', image: 'imgs/artist.png.png', preview: soundHelixTracks[2] },
            { id: 4, name: 'Luna Echo', genre: 'Ambient', followers: '1.2M', image: 'imgs/artist.png.png', preview: soundHelixTracks[3] },
            { id: 5, name: 'Atomic Sound', genre: 'Techno', followers: '1.0M', image: 'imgs/artist.png.png', preview: soundHelixTracks[4] },
            { id: 6, name: 'Cosmic Drift', genre: 'Space Rock', followers: '850K', image: 'imgs/artist.png.png', preview: soundHelixTracks[5] }
        ];

        // Assign a unique SoundHelix track to each album
        const mockAlbums = [
            { id: 1, title: 'Galaxy Sounds', artist: 'Stellar Waves', year: '2024', tracks: 12, image: 'imgs/album-01.png', preview: soundHelixTracks[6] },
            { id: 2, title: 'City Lights', artist: 'Electric Pulse', year: '2023', tracks: 10, image: 'imgs/album-02.png', preview: soundHelixTracks[7] },
            { id: 3, title: 'Future Romance', artist: 'Cyber Hearts', year: '2024', tracks: 8, image: 'imgs/album-03.png', preview: soundHelixTracks[8] },
            { id: 4, title: 'Nocturnal', artist: 'Luna Echo', year: '2024', tracks: 14, image: 'imgs/album-04.png', preview: soundHelixTracks[9] },
            { id: 5, title: 'Particle Dance', artist: 'Atomic Sound', year: '2023', tracks: 11, image: 'imgs/album-05.png', preview: soundHelixTracks[0] },
            { id: 6, title: 'Stellar Journey', artist: 'Cosmic Drift', year: '2024', tracks: 9, image: 'imgs/album-01.png', preview: soundHelixTracks[1] }
        ];

        const mockRising = [
            { id: 1, title: 'Rising Star', artist: 'New Wave', plays: '500K', change: 'up', changeValue: '+15', image: 'imgs/album-01.png', preview: 'https://cdns-preview-7.dzcdn.net/stream/c-7b7e7e7e7e7e7e7e7e7e7e7e7e7e7e7e-3.mp3' },
            { id: 2, title: 'Breakthrough', artist: 'Fresh Beat', plays: '450K', change: 'up', changeValue: '+12', image: 'imgs/album-02.png', preview: 'https://cdns-preview-8.dzcdn.net/stream/c-8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e-3.mp3' },
            { id: 3, title: 'Viral Hit', artist: 'Trend Setter', plays: '400K', change: 'up', changeValue: '+20', image: 'imgs/album-03.png', preview: 'https://cdns-preview-9.dzcdn.net/stream/c-9b9e9e9e9e9e9e9e9e9e9e9e9e9e9e9e-3.mp3' },
            { id: 4, title: 'Next Level', artist: 'Future Star', plays: '350K', change: 'up', changeValue: '+18', image: 'imgs/album-04.png', preview: 'https://cdns-preview-1.dzcdn.net/stream/c-1b1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e-3.mp3' },
            { id: 5, title: 'Momentum', artist: 'Rising Force', plays: '300K', change: 'up', changeValue: '+25', image: 'imgs/album-05.png', preview: 'https://cdns-preview-2.dzcdn.net/stream/c-2b2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e-3.mp3' }
        ];

        return {
            featured: mockSongs[0],
            songs: mockSongs,
            artists: mockArtists,
            albums: mockAlbums,
            rising: mockRising,
            statistics: {
                totalPlays: '847M',
                totalTracks: '12.4K',
                totalArtists: '3.2K',
                totalCountries: '195'
            }
        };
    }

    renderChartData(data) {
        this.renderFeaturedHero(data.featured);
        this.renderSongsChart(data.songs);
        this.renderArtistsChart(data.artists);
        this.renderAlbumsChart(data.albums);
        this.renderRisingChart(data.rising);
    }

    renderFeaturedHero(featured) {
        this.heroBgImage.src = featured.image;
        this.heroTitle.textContent = featured.title;
        this.heroArtist.textContent = `by ${featured.artist}`;
        this.heroPlays.textContent = featured.plays;
        this.heroLikes.textContent = `${Math.floor(Math.random() * 200 + 50)}K likes`;
        this.heroShares.textContent = `${Math.floor(Math.random() * 50 + 10)}K shares`;
        // Store preview URL on the play button for mini player
        this.heroPlayBtn.setAttribute('data-preview', featured.preview || '');
        this.heroPlayBtn.setAttribute('data-title', featured.title || '');
        this.heroPlayBtn.setAttribute('data-artist', featured.artist || '');
        this.heroPlayBtn.setAttribute('data-cover', featured.image || '');
    }

    renderSongsChart(songs) {
        const fallbackPreview = 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3';
        this.songsChartList.innerHTML = songs.slice(0, 10).map((song, index) => {
            const hasPreview = !!song.preview;
            const previewUrl = hasPreview ? song.preview : fallbackPreview;
            return (
                '<div class="chart-item" data-id="' + song.id + '">' +
                    '<div class="chart-position ' + (index < 3 ? 'top-3' : '') + '">' + (index + 1) + '</div>' +
                    '<div class="chart-item-image">' +
                        '<img src="' + song.image + '" alt="' + song.title + '" loading="lazy">' +
                    '</div>' +
                    '<div class="chart-item-info">' +
                        '<div class="chart-item-title">' + song.title + '</div>' +
                        '<div class="chart-item-artist">' + song.artist + '</div>' +
                    '</div>' +
                    '<div class="chart-item-stats">' +
                        '<div class="chart-item-plays">' + song.plays + '</div>' +
                    '</div>' +
                    '<div class="chart-item-actions">' +
                        '<button type="button" class="chart-action-btn play" aria-label="Play ' + song.title + '" data-preview="' + previewUrl + '"' + (hasPreview ? '' : ' data-fallback="1"') + '>' +
                            '<i class="fas fa-play"></i>' +
                        '</button>' +
                        '<button type="button" class="chart-action-btn" aria-label="Like ' + song.title + '">' +
                            '<i class="fas fa-heart"></i>' +
                        '</button>' +
                        '<button type="button" class="chart-action-btn" aria-label="Add ' + song.title + ' to playlist">' +
                            '<i class="fas fa-plus"></i>' +
                        '</button>' +
                    '</div>' +
                '</div>'
            );
        }).join('');
    }

    renderArtistsChart(artists) {
        const fallbackPreview = 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3';
        this.artistsChartGrid.innerHTML = artists.slice(0, 6).map((artist, index) => `
            <div class="chart-card" data-id="${artist.id}">
                <div class="chart-card-position">${index + 1}</div>
                <div class="chart-card-image artist">
                    <img src="${artist.image}" alt="${artist.name}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="chart-action-btn play" aria-label="Play ${artist.name}" data-preview="${artist.preview || fallbackPreview}" data-title="${artist.name}" data-artist="${artist.name}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-card-title">${artist.name}</div>
                <div class="chart-card-subtitle">${artist.genre}</div>
                <div class="chart-card-stats">${artist.followers} followers</div>
            </div>
        `).join('');
    }

    renderAlbumsChart(albums) {
        const fallbackPreview = 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3';
        this.albumsChartGrid.innerHTML = albums.slice(0, 6).map((album, index) => `
            <div class="chart-card" data-id="${album.id}">
                <div class="chart-card-position">${index + 1}</div>
                <div class="chart-card-image">
                    <img src="${album.image}" alt="${album.title}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="chart-action-btn play" aria-label="Play ${album.title}" data-preview="${album.preview || fallbackPreview}" data-title="${album.title}" data-artist="${album.artist}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-card-title">${album.title}</div>
                <div class="chart-card-subtitle">${album.artist}</div>
                <div class="chart-card-stats">${album.year} • ${album.tracks} tracks</div>
            </div>
        `).join('');
    }

    renderRisingChart(rising) {
        this.risingChartList.innerHTML = rising.slice(0, 5).map((song, index) => `
            <div class="chart-item" data-id="${song.id}">
                <div class="chart-position">${index + 1}</div>
                <div class="chart-item-image">
                    <img src="${song.image}" alt="${song.title}" loading="lazy">
                </div>
                <div class="chart-item-info">
                    <div class="chart-item-title">${song.title}</div>
                    <div class="chart-item-artist">${song.artist}</div>
                </div>
                <div class="chart-item-stats">
                    <div class="chart-item-plays">${song.plays}</div>
                </div>
                <div class="chart-item-actions">
                    <button type="button" class="chart-action-btn play" aria-label="Play ${song.title}" data-preview="${song.preview || ''}">
                        <i class="fas fa-play"></i>
                    </button>
                    <button type="button" class="chart-action-btn" aria-label="Like ${song.title}">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button type="button" class="chart-action-btn" aria-label="Add ${song.title} to playlist">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateStatistics(stats) {
        this.totalPlays.textContent = stats.totalPlays;
        this.totalTracks.textContent = stats.totalTracks;
        this.totalArtists.textContent = stats.totalArtists;
        this.totalCountries.textContent = stats.totalCountries;
    }

    showLoadingState() {
        this.loadingState.classList.remove('hidden');
        document.querySelectorAll('.chart-section').forEach(section => {
            section.style.opacity = '0.5';
        });
    }

    hideLoadingState() {
        this.loadingState.classList.add('hidden');
        document.querySelectorAll('.chart-section').forEach(section => {
            section.style.opacity = '1';
        });
    }

    playFeaturedTrack() {
        // Simulate playing the featured track
        this.heroPlayBtn.innerHTML = '<i class="fas fa-pause"></i> Playing...';
        this.announceAction('Playing featured track');

        setTimeout(() => {
            this.heroPlayBtn.innerHTML = '<i class="fas fa-play"></i> Play Now';
        }, 3000);
    }

    likeFeaturedTrack() {
        const isLiked = this.heroLikeBtn.classList.contains('liked');

        if (isLiked) {
            this.heroLikeBtn.classList.remove('liked');
            this.heroLikeBtn.innerHTML = '<i class="fas fa-heart"></i> Like';
            this.announceAction('Removed from liked songs');
        } else {
            this.heroLikeBtn.classList.add('liked');
            this.heroLikeBtn.innerHTML = '<i class="fas fa-heart" style="color: var(--cosmic-pink);"></i> Liked';
            this.announceAction('Added to liked songs');
        }
    }

    addFeaturedToPlaylist() {
        // Simulate adding to playlist
        this.heroAddBtn.innerHTML = '<i class="fas fa-check"></i> Added';
        this.announceAction('Added to playlist');

        setTimeout(() => {
            this.heroAddBtn.innerHTML = '<i class="fas fa-plus"></i> Add to Playlist';
        }, 2000);
    }

    viewAllCharts(type) {
        // Simulate navigation to full charts page
        const chartNames = {
            songs: 'Top 100 Songs',
            artists: 'Top 50 Artists',
            albums: 'Top 50 Albums',
            rising: 'Rising Stars'
        };

        this.announceAction(`Viewing ${chartNames[type]} chart`);
        // In a real app, this would navigate to a dedicated page
        console.log(`Navigating to ${chartNames[type]} page`);
    }

    refreshCharts() {
        this.announceAction('Refreshing charts...');
        this.loadChartData();
    }

    announceChartChange(chartType) {
        const chartNames = {
            global: 'Global Charts',
            country: 'Country Charts',
            viral: 'Viral Charts',
            new: 'New Releases'
        };

        this.announceAction(`Switched to ${chartNames[chartType]}`);
    }

    announceAction(message) {
        this.liveRegion.textContent = message;

        // Clear the message after a short delay
        setTimeout(() => {
            this.liveRegion.textContent = '';
        }, 1000);
    }
}

// Initialize the trending charts manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chartsManager = new TrendingChartsManager();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TrendingChartsManager;
}