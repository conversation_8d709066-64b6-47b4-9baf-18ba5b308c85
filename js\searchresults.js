// Enhanced Search Results Management System with Backend Integration
class SearchResultsManager {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.currentQuery = '';
        this.currentFilters = {
            contentType: 'all',
            genre: '',
            sort: 'relevance'
        };
        this.searchTimeout = null;
        this.isSearching = false;
        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.loadInitialState();
    }

    bindElements() {
        this.searchInput = document.getElementById('searchInput');
        this.clearSearchBtn = document.getElementById('clearSearchBtn');
        this.searchFiltersBtn = document.getElementById('searchFiltersBtn');
        this.searchFiltersPanel = document.getElementById('searchFiltersPanel');
        this.contentTypeFilter = document.getElementById('contentTypeFilter');
        this.genreFilter = document.getElementById('genreFilter');
        this.sortFilter = document.getElementById('sortFilter');
        this.clearFiltersBtn = document.getElementById('clearFiltersBtn');
        this.applyFiltersBtn = document.getElementById('applyFiltersBtn');
        this.searchQueryInfo = document.getElementById('searchQueryInfo');
        this.quickSuggestions = document.getElementById('quickSuggestions');
        this.searchResultsSections = document.getElementById('searchResultsSections');
        this.noResultsState = document.getElementById('noResultsState');
        this.loadingState = document.getElementById('loadingState');
        this.liveRegion = document.getElementById('aria-live-region');

        // Result sections
        this.topResultSection = document.getElementById('topResultSection');
        this.topResultCard = document.getElementById('topResultCard');
        this.songsGrid = document.getElementById('songsGrid');
        this.artistsGrid = document.getElementById('artistsGrid');
        this.albumsGrid = document.getElementById('albumsGrid');
        this.playlistsGrid = document.getElementById('playlistsGrid');
    }

    bindEvents() {
        // Search input
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch(this.searchInput.value);
            }
        });

        // Clear search
        this.clearSearchBtn.addEventListener('click', () => {
            this.clearSearch();
        });

        // Filters
        this.searchFiltersBtn.addEventListener('click', () => {
            this.toggleFiltersPanel();
        });

        this.clearFiltersBtn.addEventListener('click', () => {
            this.clearFilters();
        });

        this.applyFiltersBtn.addEventListener('click', () => {
            this.applyFilters();
        });

        // Filter changes
        [this.contentTypeFilter, this.genreFilter, this.sortFilter].forEach(filter => {
            filter.addEventListener('change', () => {
                this.updateFilters();
            });
        });

        // Quick suggestions
        document.querySelectorAll('.suggestion-tag').forEach(tag => {
            tag.addEventListener('click', (e) => {
                const query = e.target.dataset.query;
                this.searchInput.value = query;
                this.performSearch(query);
            });
        });

        // View all buttons
        document.querySelectorAll('.view-all-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const type = e.target.dataset.type;
                this.viewAllResults(type);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.searchInput.focus();
            }
            if (e.key === 'Escape') {
                this.searchFiltersPanel.classList.add('hidden');
                this.searchFiltersBtn.classList.remove('active');
            }
        });
    }

    loadInitialState() {
        // Check URL parameters for search query
        const urlParams = new URLSearchParams(window.location.search);
        const query = urlParams.get('q');

        if (query) {
            this.searchInput.value = query;
            this.performSearch(query);
        } else {
            this.showInitialState();
        }
    }

    handleSearchInput(value) {
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // Debounce search
        this.searchTimeout = setTimeout(() => {
            if (value.trim()) {
                this.performSearch(value.trim());
            } else {
                this.showInitialState();
            }
        }, 300);
    }

    async performSearch(query) {
        if (this.isSearching) return;

        this.currentQuery = query;
        this.isSearching = true;

        // Update URL
        const url = new URL(window.location);
        url.searchParams.set('q', query);
        window.history.pushState({}, '', url);

        // Show loading state
        this.showLoadingState();

        // Update search info
        this.updateSearchInfo(query);

        try {
            // Call backend search API with filters
            const searchParams = new URLSearchParams({
                q: query,
                type: this.currentFilters.contentType,
                sort: this.currentFilters.sort
            });

            if (this.currentFilters.genre) {
                searchParams.append('genre', this.currentFilters.genre);
            }

            const response = await fetch(`${this.apiBase}/search?${searchParams}`);

            if (response.ok) {
                const results = await response.json();
                this.displayBackendResults(results);
            } else {
                throw new Error('Search API failed');
            }
        } catch (error) {
            console.error('Search error:', error);
            // Fallback to mock results
            this.displaySearchResults(query);
        } finally {
            this.isSearching = false;
        }
    }

    displayBackendResults(results) {
        if (results.total === 0) {
            this.showNoResults();
            return;
        }

        this.showResultsSections();

        // Render top result
        if (results.topResult) {
            this.renderTopResult(results.topResult);
        }

        // Render all sections with real data
        this.renderBackendSongs(results.tracks || []);
        this.renderBackendArtists(results.artists || []);
        this.renderBackendAlbums(results.albums || []);
        this.renderBackendPlaylists(results.playlists || []);

        this.announceResults(results.total, results.query);
        console.log('✅ Search results rendered:', results);
    }

    displaySearchResults(query) {
        const results = this.generateMockResults(query);

        if (results.total === 0) {
            this.showNoResults();
            return;
        }

        this.showResultsSections();
        this.renderTopResult(results.topResult);
        this.renderSongs(results.songs);
        this.renderArtists(results.artists);
        this.renderAlbums(results.albums);
        this.renderPlaylists(results.playlists);

        this.announceResults(results.total, query);
    }

    generateMockResults(query) {
        // Simulate search results based on query
        const mockData = {
            songs: [
                { id: 1, title: 'Cosmic Dreams', artist: 'Stellar Waves', album: 'Galaxy Sounds', duration: '3:45', image: 'imgs/album-01.png' },
                { id: 2, title: 'Neon Nights', artist: 'Electric Pulse', album: 'City Lights', duration: '4:12', image: 'imgs/album-02.png' },
                { id: 3, title: 'Digital Love', artist: 'Cyber Hearts', album: 'Future Romance', duration: '3:28', image: 'imgs/album-03.png' },
                { id: 4, title: 'Midnight Vibes', artist: 'Luna Echo', album: 'Nocturnal', duration: '4:01', image: 'imgs/album-04.png' },
                { id: 5, title: 'Quantum Beat', artist: 'Atomic Sound', album: 'Particle Dance', duration: '3:33', image: 'imgs/album-05.png' }
            ],
            artists: [
                { id: 1, name: 'Stellar Waves', genre: 'Electronic', followers: '2.1M', image: 'imgs/artist-01.png' },
                { id: 2, name: 'Electric Pulse', genre: 'Synthwave', followers: '1.8M', image: 'imgs/artist-02.png' },
                { id: 3, name: 'Cyber Hearts', genre: 'Future Pop', followers: '950K', image: 'imgs/artist-03.png' },
                { id: 4, name: 'Luna Echo', genre: 'Ambient', followers: '1.2M', image: 'imgs/artist-04.png' }
            ],
            albums: [
                { id: 1, title: 'Galaxy Sounds', artist: 'Stellar Waves', year: '2024', tracks: 12, image: 'imgs/album-01.png' },
                { id: 2, title: 'City Lights', artist: 'Electric Pulse', year: '2023', tracks: 10, image: 'imgs/album-02.png' },
                { id: 3, title: 'Future Romance', artist: 'Cyber Hearts', year: '2024', tracks: 8, image: 'imgs/album-03.png' }
            ],
            playlists: [
                { id: 1, title: 'Electronic Essentials', creator: 'BansheeBlast', tracks: 50, likes: '12K', image: 'imgs/playlist-01.png' },
                { id: 2, title: 'Chill Synthwave', creator: 'MusicLover', tracks: 35, likes: '8.5K', image: 'imgs/playlist-02.png' },
                { id: 3, title: 'Future Beats', creator: 'ElectroFan', tracks: 42, likes: '15K', image: 'imgs/playlist-03.png' }
            ]
        };

        // Filter results based on query (simple contains check)
        const filteredSongs = mockData.songs.filter(song =>
            song.title.toLowerCase().includes(query.toLowerCase()) ||
            song.artist.toLowerCase().includes(query.toLowerCase())
        );

        const filteredArtists = mockData.artists.filter(artist =>
            artist.name.toLowerCase().includes(query.toLowerCase()) ||
            artist.genre.toLowerCase().includes(query.toLowerCase())
        );

        const filteredAlbums = mockData.albums.filter(album =>
            album.title.toLowerCase().includes(query.toLowerCase()) ||
            album.artist.toLowerCase().includes(query.toLowerCase())
        );

        const filteredPlaylists = mockData.playlists.filter(playlist =>
            playlist.title.toLowerCase().includes(query.toLowerCase()) ||
            playlist.creator.toLowerCase().includes(query.toLowerCase())
        );

        // Determine top result
        let topResult = null;
        if (filteredSongs.length > 0) {
            topResult = { type: 'song', data: filteredSongs[0] };
        } else if (filteredArtists.length > 0) {
            topResult = { type: 'artist', data: filteredArtists[0] };
        } else if (filteredAlbums.length > 0) {
            topResult = { type: 'album', data: filteredAlbums[0] };
        }

        const total = filteredSongs.length + filteredArtists.length + filteredAlbums.length + filteredPlaylists.length;

        return {
            total,
            topResult,
            songs: filteredSongs.slice(0, 5),
            artists: filteredArtists.slice(0, 6),
            albums: filteredAlbums.slice(0, 6),
            playlists: filteredPlaylists.slice(0, 6)
        };
    }

    renderTopResult(topResult) {
        if (!topResult) {
            this.topResultSection.classList.add('hidden');
            return;
        }

        this.topResultSection.classList.remove('hidden');
        const { type, data } = topResult;

        let html = '';
        if (type === 'song') {
            html = `
                <div class="top-result-image">
                    <img src="${data.image}" alt="${data.title}" loading="lazy">
                </div>
                <div class="top-result-info">
                    <div class="top-result-type">Song</div>
                    <h3 class="top-result-title">${data.title}</h3>
                    <p class="top-result-subtitle">${data.artist} • ${data.album}</p>
                    <div class="top-result-actions">
                        <button type="button" class="play-btn" aria-label="Play ${data.title}">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="action-btn">
                            <i class="fas fa-heart"></i>
                            Like
                        </button>
                        <button type="button" class="action-btn">
                            <i class="fas fa-plus"></i>
                            Add to Playlist
                        </button>
                    </div>
                </div>
            `;
        } else if (type === 'artist') {
            html = `
                <div class="top-result-image">
                    <img src="${data.image}" alt="${data.name}" loading="lazy">
                </div>
                <div class="top-result-info">
                    <div class="top-result-type">Artist</div>
                    <h3 class="top-result-title">${data.name}</h3>
                    <p class="top-result-subtitle">${data.followers} followers • ${data.genre}</p>
                    <div class="top-result-actions">
                        <button type="button" class="play-btn" aria-label="Play ${data.name}">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="action-btn">
                            <i class="fas fa-user-plus"></i>
                            Follow
                        </button>
                    </div>
                </div>
            `;
        }

        this.topResultCard.innerHTML = html;
    }

    renderSongs(songs) {
        if (songs.length === 0) {
            document.getElementById('songsSection').classList.add('hidden');
            return;
        }

        document.getElementById('songsSection').classList.remove('hidden');
        this.songsGrid.innerHTML = songs.map(song => `
            <div class="song-result-card" data-id="${song.id}">
                <div class="song-image">
                    <img src="${song.image}" alt="${song.title}" loading="lazy">
                </div>
                <div class="song-info">
                    <div class="song-title">${song.title}</div>
                    <div class="song-artist">${song.artist}</div>
                </div>
                <div class="song-duration">${song.duration}</div>
                <div class="song-actions">
                    <button type="button" class="song-action-btn" aria-label="Play ${song.title}">
                        <i class="fas fa-play"></i>
                    </button>
                    <button type="button" class="song-action-btn" aria-label="Like ${song.title}">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button type="button" class="song-action-btn" aria-label="Add ${song.title} to playlist">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderArtists(artists) {
        if (artists.length === 0) {
            document.getElementById('artistsSection').classList.add('hidden');
            return;
        }

        document.getElementById('artistsSection').classList.remove('hidden');
        this.artistsGrid.innerHTML = artists.map(artist => `
            <div class="result-card" data-id="${artist.id}">
                <div class="result-image artist">
                    <img src="${artist.image}" alt="${artist.name}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-btn" aria-label="Play ${artist.name}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="result-title">${artist.name}</div>
                <div class="result-subtitle">${artist.genre}</div>
                <div class="result-meta">${artist.followers} followers</div>
            </div>
        `).join('');
    }

    renderAlbums(albums) {
        if (albums.length === 0) {
            document.getElementById('albumsSection').classList.add('hidden');
            return;
        }

        document.getElementById('albumsSection').classList.remove('hidden');
        this.albumsGrid.innerHTML = albums.map(album => `
            <div class="result-card" data-id="${album.id}">
                <div class="result-image">
                    <img src="${album.image}" alt="${album.title}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-btn" aria-label="Play ${album.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="result-title">${album.title}</div>
                <div class="result-subtitle">${album.artist}</div>
                <div class="result-meta">${album.year} • ${album.tracks} tracks</div>
            </div>
        `).join('');
    }

    renderPlaylists(playlists) {
        if (playlists.length === 0) {
            document.getElementById('playlistsSection').classList.add('hidden');
            return;
        }

        document.getElementById('playlistsSection').classList.remove('hidden');
        this.playlistsGrid.innerHTML = playlists.map(playlist => `
            <div class="result-card" data-id="${playlist.id}">
                <div class="result-image">
                    <img src="${playlist.image}" alt="${playlist.title}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-btn" aria-label="Play ${playlist.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="result-title">${playlist.title}</div>
                <div class="result-subtitle">by ${playlist.creator}</div>
                <div class="result-meta">${playlist.tracks} tracks • ${playlist.likes} likes</div>
            </div>
        `).join('');
    }

    showInitialState() {
        this.hideAllStates();
        this.quickSuggestions.classList.remove('hidden');
        this.searchQueryInfo.classList.remove('hidden');

        // Reset search info
        this.searchQueryInfo.querySelector('h1').textContent = 'Search Results';
        this.searchQueryInfo.querySelector('p').textContent = 'Enter a search term to find your favorite music';
    }

    showLoadingState() {
        this.hideAllStates();
        this.loadingState.classList.remove('hidden');
    }

    showNoResults() {
        this.hideAllStates();
        this.noResultsState.classList.remove('hidden');
    }

    showResultsSections() {
        this.hideAllStates();
        this.searchResultsSections.classList.remove('hidden');
    }

    hideAllStates() {
        this.quickSuggestions.classList.add('hidden');
        this.searchResultsSections.classList.add('hidden');
        this.noResultsState.classList.add('hidden');
        this.loadingState.classList.add('hidden');
    }

    updateSearchInfo(query) {
        this.searchQueryInfo.querySelector('h1').textContent = `Results for "${query}"`;
        this.searchQueryInfo.querySelector('p').textContent = `Searching for "${query}" in songs, artists, albums, and playlists`;
    }

    clearSearch() {
        this.searchInput.value = '';
        this.currentQuery = '';

        // Update URL
        const url = new URL(window.location);
        url.searchParams.delete('q');
        window.history.pushState({}, '', url);

        this.showInitialState();
        this.announceAction('Search cleared');
    }

    toggleFiltersPanel() {
        const isHidden = this.searchFiltersPanel.classList.contains('hidden');

        if (isHidden) {
            this.searchFiltersPanel.classList.remove('hidden');
            this.searchFiltersBtn.classList.add('active');
        } else {
            this.searchFiltersPanel.classList.add('hidden');
            this.searchFiltersBtn.classList.remove('active');
        }
    }

    clearFilters() {
        this.contentTypeFilter.value = 'all';
        this.genreFilter.value = '';
        this.sortFilter.value = 'relevance';

        this.currentFilters = {
            contentType: 'all',
            genre: '',
            sort: 'relevance'
        };

        this.announceAction('Filters cleared');

        // Re-search if there's a current query
        if (this.currentQuery) {
            this.performSearch(this.currentQuery);
        }
    }

    applyFilters() {
        this.updateFilters();
        this.toggleFiltersPanel();

        // Re-search if there's a current query
        if (this.currentQuery) {
            this.performSearch(this.currentQuery);
        }

        this.announceAction('Filters applied');
    }

    updateFilters() {
        this.currentFilters = {
            contentType: this.contentTypeFilter.value,
            genre: this.genreFilter.value,
            sort: this.sortFilter.value
        };
    }

    viewAllResults(type) {
        // Simulate navigation to dedicated results page
        const query = this.currentQuery;
        const url = `searchresults.html?q=${encodeURIComponent(query)}&type=${type}`;
        window.location.href = url;
    }

    // ===== BACKEND RENDERING METHODS =====

    renderBackendSongs(tracks) {
        if (tracks.length === 0) {
            document.getElementById('songsSection').classList.add('hidden');
            return;
        }

        document.getElementById('songsSection').classList.remove('hidden');
        this.songsGrid.innerHTML = tracks.map(track => `
            <div class="song-result-card" data-id="${track.id}">
                <div class="song-image">
                    <img src="${track.cover}" alt="${track.title}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-btn" aria-label="Play ${track.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="song-info">
                    <div class="song-title">${track.title}</div>
                    <div class="song-artist">${track.artist}</div>
                    <div class="song-album">${track.album}</div>
                </div>
                <div class="song-duration">${track.duration}</div>
                <div class="song-actions">
                    <button type="button" class="song-action-btn like-btn" data-track-id="${track.id}" aria-label="Like ${track.title}">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button type="button" class="song-action-btn more-btn" aria-label="More options for ${track.title}">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>
        `).join('');

        // Add event listeners
        this.bindSongCardEvents();
    }

    renderBackendArtists(artists) {
        if (artists.length === 0) {
            document.getElementById('artistsSection').classList.add('hidden');
            return;
        }

        document.getElementById('artistsSection').classList.remove('hidden');
        this.artistsGrid.innerHTML = artists.map(artist => `
            <div class="result-card" data-id="${artist.id}">
                <div class="result-image artist">
                    <img src="${artist.image}" alt="${artist.name}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-btn" aria-label="Play ${artist.name}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="result-title">${artist.name}</div>
                <div class="result-subtitle">${artist.genre}</div>
                <div class="result-meta">${artist.followers} followers</div>
            </div>
        `).join('');
    }

    renderBackendAlbums(albums) {
        if (albums.length === 0) {
            document.getElementById('albumsSection').classList.add('hidden');
            return;
        }

        document.getElementById('albumsSection').classList.remove('hidden');
        this.albumsGrid.innerHTML = albums.map(album => `
            <div class="result-card" data-id="${album.id}">
                <div class="result-image">
                    <img src="${album.image}" alt="${album.title}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-btn" aria-label="Play ${album.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="result-title">${album.title}</div>
                <div class="result-subtitle">${album.artist}</div>
                <div class="result-meta">${album.year} • ${album.tracks} tracks</div>
            </div>
        `).join('');
    }

    renderBackendPlaylists(playlists) {
        if (playlists.length === 0) {
            document.getElementById('playlistsSection').classList.add('hidden');
            return;
        }

        document.getElementById('playlistsSection').classList.remove('hidden');
        this.playlistsGrid.innerHTML = playlists.map(playlist => `
            <div class="result-card" data-id="${playlist.id}">
                <div class="result-image">
                    <img src="${playlist.cover || 'imgs/playlist-01.png'}" alt="${playlist.name}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-btn" aria-label="Play ${playlist.name}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="result-title">${playlist.name}</div>
                <div class="result-subtitle">${playlist.description || 'Playlist'}</div>
                <div class="result-meta">${playlist.tracks?.length || 0} tracks</div>
            </div>
        `).join('');
    }

    bindSongCardEvents() {
        // Play button events
        this.songsGrid.querySelectorAll('.play-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const card = btn.closest('.song-result-card');
                const trackId = card.dataset.id;
                this.playTrack(trackId);
            });
        });

        // Like button events
        this.songsGrid.querySelectorAll('.like-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const trackId = btn.dataset.trackId;
                this.toggleLike(trackId, btn);
            });
        });
    }

    playTrack(trackId) {
        this.announceAction(`Playing track ${trackId}`);
        console.log(`🎵 Playing track: ${trackId}`);
    }

    toggleLike(trackId, button) {
        const isLiked = button.classList.contains('liked');
        if (isLiked) {
            button.classList.remove('liked');
            this.announceAction('Removed from liked songs');
        } else {
            button.classList.add('liked');
            this.announceAction('Added to liked songs');
        }
        console.log(`❤️ Toggled like for track: ${trackId}`);
    }

    announceResults(total, query) {
        const message = `Found ${total} result${total !== 1 ? 's' : ''} for "${query}"`;
        this.announceAction(message);
    }

    announceAction(message) {
        this.liveRegion.textContent = message;

        // Clear the message after a short delay
        setTimeout(() => {
            this.liveRegion.textContent = '';
        }, 1000);
    }
}

// Initialize the search results manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.searchManager = new SearchResultsManager();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchResultsManager;
}