// Shared Mini Player Component for Banshee Music App
class MiniPlayer {
    constructor() {
        this.isVisible = false;
        this.currentTrack = null;
        this.currentTracks = [];
        this.currentIndex = -1;
        this.audio = null;
        this.isPlaying = false;
        this.duration = 0;
        this.currentTime = 0;
        this.volume = 1;
        
        this.init();
    }

    init() {
        this.createMiniPlayer();
        this.bindElements();
        this.bindEvents();
        this.createAudioElement();
    }

    createMiniPlayer() {
        // Remove any existing mini players
        const existingPlayers = document.querySelectorAll('.mini-player, #miniPlayer, #miniPlayerTest');
        existingPlayers.forEach(player => player.remove());

        // Create the unified mini player HTML
        const miniPlayerHTML = `
            <div class="banshee-mini-player hidden" id="bansheeMiniPlayer">
                <div class="mini-player-content">
                    <div class="mini-player-info">
                        <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                        <div class="mini-player-text">
                            <h4 id="miniPlayerTitle">Track Title</h4>
                            <p id="miniPlayerArtist">Artist Name</p>
                        </div>
                    </div>
                    <div class="mini-player-controls">
                        <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                            <i class="fas fa-step-backward"></i>
                        </button>
                        <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                            <i class="fas fa-step-forward"></i>
                        </button>
                    </div>
                    <div class="mini-player-progress">
                        <div class="progress-bar" id="miniProgressBar">
                            <div class="progress-fill" id="miniProgressFill"></div>
                        </div>
                        <div class="time-display">
                            <span id="miniCurrentTime">0:00</span>
                            <span id="miniDuration">0:00</span>
                        </div>
                    </div>
                    <div class="mini-player-actions">
                        <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                            <i class="fas fa-volume-up"></i>
                        </button>
                        <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Insert mini player at the end of body
        document.body.insertAdjacentHTML('beforeend', miniPlayerHTML);
    }

    bindElements() {
        this.miniPlayer = document.getElementById('bansheeMiniPlayer');
        this.artwork = document.getElementById('miniPlayerArtwork');
        this.title = document.getElementById('miniPlayerTitle');
        this.artist = document.getElementById('miniPlayerArtist');
        this.prevBtn = document.getElementById('miniPrevBtn');
        this.playPauseBtn = document.getElementById('miniPlayPauseBtn');
        this.nextBtn = document.getElementById('miniNextBtn');
        this.progressBar = document.getElementById('miniProgressBar');
        this.progressFill = document.getElementById('miniProgressFill');
        this.currentTimeEl = document.getElementById('miniCurrentTime');
        this.durationEl = document.getElementById('miniDuration');
        this.volumeBtn = document.getElementById('miniVolumeBtn');
        this.expandBtn = document.getElementById('miniExpandBtn');
        this.closeBtn = document.getElementById('miniCloseBtn');
    }

    bindEvents() {
        // Control buttons
        this.playPauseBtn?.addEventListener('click', () => this.togglePlayPause());
        this.prevBtn?.addEventListener('click', () => this.previousTrack());
        this.nextBtn?.addEventListener('click', () => this.nextTrack());
        this.closeBtn?.addEventListener('click', () => this.hide());
        this.expandBtn?.addEventListener('click', () => this.expandPlayer());
        this.volumeBtn?.addEventListener('click', () => this.toggleMute());

        // Progress bar click
        this.progressBar?.addEventListener('click', (e) => this.seekTo(e));

        // Listen for play button clicks throughout the app
        document.addEventListener('click', (e) => {
            const playBtn = e.target.closest('.play-button, .chart-action-btn.play, .play-btn');
            if (playBtn && (playBtn.dataset.preview || playBtn.dataset.src)) {
                this.handlePlayButtonClick(playBtn);
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (this.isVisible) {
                switch (e.code) {
                    case 'Space':
                        if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                            e.preventDefault();
                            this.togglePlayPause();
                        }
                        break;
                    case 'ArrowLeft':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            this.previousTrack();
                        }
                        break;
                    case 'ArrowRight':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            this.nextTrack();
                        }
                        break;
                    case 'Escape':
                        e.preventDefault();
                        this.hide();
                        break;
                }
            }
        });
    }

    createAudioElement() {
        // Remove existing audio elements
        const existingAudio = document.querySelectorAll('#bansheeAudio, #audioTest, #audio');
        existingAudio.forEach(audio => {
            if (audio.id === 'bansheeAudio') audio.remove();
        });

        // Create new audio element
        this.audio = document.createElement('audio');
        this.audio.id = 'bansheeAudio';
        this.audio.preload = 'none';
        document.body.appendChild(this.audio);

        // Audio event listeners
        this.audio.addEventListener('loadedmetadata', () => this.updateDuration());
        this.audio.addEventListener('timeupdate', () => this.updateProgress());
        this.audio.addEventListener('ended', () => this.handleTrackEnd());
        this.audio.addEventListener('play', () => this.handlePlay());
        this.audio.addEventListener('pause', () => this.handlePause());
        this.audio.addEventListener('error', (e) => this.handleError(e));
        this.audio.addEventListener('canplay', () => this.handleCanPlay());
    }

    handlePlayButtonClick(playBtn) {
        const preview = playBtn.dataset.preview || playBtn.dataset.src;
        const title = playBtn.dataset.title || playBtn.closest('.card, .chart-item, .track-item')?.querySelector('h3, .title, .track-title')?.textContent || 'Unknown Track';
        const artist = playBtn.dataset.artist || playBtn.closest('.card, .chart-item, .track-item')?.querySelector('p, .artist, .track-artist')?.textContent || 'Unknown Artist';
        const image = playBtn.dataset.image || playBtn.closest('.card, .chart-item, .track-item')?.querySelector('img')?.src || 'imgs/album-01.png';

        if (preview) {
            this.playTrack({
                preview,
                title,
                artist,
                image,
                id: playBtn.dataset.id || Date.now().toString()
            });
        }
    }

    playTrack(track) {
        this.currentTrack = track;
        this.updateUI();
        this.show();

        if (this.audio.src !== track.preview) {
            this.audio.src = track.preview;
            this.audio.currentTime = 0;
        }

        this.audio.play().catch(error => {
            console.error('❌ Failed to play track:', error);
            this.handleError(error);
        });
    }

    togglePlayPause() {
        if (!this.audio.src) return;

        if (this.audio.paused) {
            this.audio.play().catch(error => {
                console.error('❌ Failed to play:', error);
                this.handleError(error);
            });
        } else {
            this.audio.pause();
        }
    }

    previousTrack() {
        if (this.currentTracks.length > 1 && this.currentIndex > 0) {
            this.currentIndex--;
            this.playTrack(this.currentTracks[this.currentIndex]);
        }
    }

    nextTrack() {
        if (this.currentTracks.length > 1 && this.currentIndex < this.currentTracks.length - 1) {
            this.currentIndex++;
            this.playTrack(this.currentTracks[this.currentIndex]);
        } else if (this.currentTracks.length > 1) {
            // Loop back to first track
            this.currentIndex = 0;
            this.playTrack(this.currentTracks[this.currentIndex]);
        }
    }

    seekTo(e) {
        if (!this.audio.duration) return;

        const rect = this.progressBar.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;
        this.audio.currentTime = percent * this.audio.duration;
    }

    updateUI() {
        if (!this.currentTrack) return;

        this.artwork.src = this.currentTrack.image || 'imgs/album-01.png';
        this.artwork.alt = this.currentTrack.title || 'Track artwork';
        this.title.textContent = this.currentTrack.title || 'Unknown Track';
        this.artist.textContent = this.currentTrack.artist || 'Unknown Artist';
    }

    updateProgress() {
        if (!this.audio.duration) return;

        this.currentTime = this.audio.currentTime;
        const percent = (this.currentTime / this.audio.duration) * 100;
        
        this.progressFill.style.width = `${percent}%`;
        this.currentTimeEl.textContent = this.formatTime(this.currentTime);
    }

    updateDuration() {
        this.duration = this.audio.duration || 0;
        this.durationEl.textContent = this.formatTime(this.duration);
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    handlePlay() {
        this.isPlaying = true;
        this.playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
        this.playPauseBtn.setAttribute('aria-label', 'Pause');
    }

    handlePause() {
        this.isPlaying = false;
        this.playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
        this.playPauseBtn.setAttribute('aria-label', 'Play');
    }

    handleTrackEnd() {
        this.nextTrack();
    }

    handleError(error) {
        console.error('❌ Mini player audio error:', error);
        this.title.textContent = 'Error loading track';
        this.handlePause();
    }

    handleCanPlay() {
        console.log('✅ Track ready to play:', this.currentTrack?.title);
    }

    show() {
        this.isVisible = true;
        this.miniPlayer?.classList.remove('hidden');
        this.miniPlayer?.classList.add('visible');
    }

    hide() {
        this.isVisible = false;
        this.miniPlayer?.classList.add('hidden');
        this.miniPlayer?.classList.remove('visible');
        this.audio?.pause();
        this.audio.src = '';
        this.currentTrack = null;
    }

    toggleMute() {
        if (this.audio.volume > 0) {
            this.audio.volume = 0;
            this.volumeBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
        } else {
            this.audio.volume = this.volume;
            this.volumeBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        }
    }

    expandPlayer() {
        // Navigate to full player page
        window.location.href = 'player.html';
    }

    // Public API methods
    getCurrentTrack() {
        return this.currentTrack;
    }

    isPlayerVisible() {
        return this.isVisible;
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        this.audio.volume = this.volume;
    }
}

// Create global instance
window.bansheeMiniPlayer = new MiniPlayer();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MiniPlayer;
}
